'use client';

import { useState, useEffect, useRef } from 'react';
import { FaCalculator, FaChartBar, FaPaperPlane } from 'react-icons/fa';
import { useMutation } from 'react-query';
import {
  sendLeadCalculatorMessage,
  generateSessionId,
} from '../../../src/actions/lead_calculator';
import { ILeadCalculatorSession } from '../../../src/types/lead_calculator';
import EmailModal from '@/components/agents/lead-cost-calculator/EmailModal';
import Image from 'next/image';
import Head from 'next/head';
import { getMetaTags, logApiErrorAndShowToastMessage } from 'src/utils';
import Link from 'next/link';
import { BiBook, BiSearch } from 'react-icons/bi';

interface Message {
  id: string;
  role: 'assistant' | 'user';
  content: string;
  showInput?: boolean;
  inputType?: 'text' | 'email';
  placeholder?: string;
  loading?: boolean;
  result?: {
    costRange: string;
    industry: string;
    city: string;
    channel: string;
    productPrice: string;
  };
  apiResponse?: boolean;
}

export default function LeadCostCalculator() {
  const [chatStarted, setChatStarted] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [session, setSession] = useState<ILeadCalculatorSession | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessageMutation = useMutation(sendLeadCalculatorMessage, {
    onError: (error: Error) => {
      logApiErrorAndShowToastMessage(
        error,
        'LeadCostCalculator.sendMessageMutation',
      );
    },
  });

  const handleEmailSubmit = (email: string) => {
    const sessionId = generateSessionId();
    const newSession: ILeadCalculatorSession = {
      sessionId,
      email,
      conversationData: {},
    };
    setSession(newSession);
    setShowEmailModal(false);
    setChatStarted(true);

    sendMessageMutation.mutate(
      {
        sessionId,
        message:
          "Hi! Ready to estimate your lead cost? What's your business industry/product?",
        email,
      },
      {
        onSuccess: (response) => {
          const botMessage: Message = {
            id: 'bot-1',
            role: 'assistant',
            content: response.data.message,
            apiResponse: true,
          };
          setMessages([botMessage]);
        },
      },
    );
  };

  const handleSendMessage = (content: string) => {
    if (!content.trim()) return;

    if (!chatStarted && !session) {
      setShowEmailModal(true);
      return;
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: content,
    };

    setMessages((prev) => [...prev, userMessage]);
    setCurrentInput('');

    const loadingMessage: Message = {
      id: `loading-${Date.now()}`,
      role: 'assistant',
      content: 'Thinking...',
      loading: true,
    };
    setMessages((prev) => [...prev, loadingMessage]);

    if (session) {
      sendMessageMutation.mutate(
        {
          sessionId: session.sessionId,
          message: content,
          email: session.email,
        },
        {
          onSuccess: (response) => {
            setMessages((prev) => prev.filter((msg) => !msg.loading));
            const botMessage: Message = {
              id: Date.now().toString(),
              role: 'assistant',
              content: response.data.message,
              apiResponse: true,
            };
            setMessages((prev) => [...prev, botMessage]);

            if (
              response?.data?.cpl_data &&
              Object.keys(response?.data?.cpl_data).length > 0
            ) {
              const currencySymbol =
                response?.data?.cpl_data?.currency === 'USD' ? '$' : '₹';
              const resultMessage: Message = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: 'Cost Per Lead Analysis',
                result: {
                  costRange: `${currencySymbol}${response?.data?.cpl_data?.cpl_range_lower} - ${currencySymbol}${response?.data?.cpl_data?.cpl_range_upper}`,
                  industry: response?.data?.cpl_data?.parameters.industry,
                  city: response?.data?.cpl_data?.parameters.city_category,
                  channel: response?.data?.cpl_data?.parameters.channel,
                  productPrice:
                    response?.data?.cpl_data?.parameters.price_range,
                },
              };
              setMessages((prev) => [...prev, resultMessage]);
            }
          },
        },
      );
    }
  };

  return (
    <>
      <Head>{getMetaTags('/marketing-ai-tools/lead-cost-calculator')}</Head>
      <div className="flex h-screen font-poppins">
        <div className="hidden lg:flex w-80 bg-primary2 text-white p-6 flex-col">
          <div className="flex items-center mb-8">
            <Image
              src={'/images/groweasy-logo-square.png'}
              alt="Groweasy Logo"
              width={40}
              height={40}
            />
            <p className="text-xl font-semibold tracking-tight ml-3">
              GrowEasy
            </p>
          </div>

          <nav className="space-y-4 flex-1">
            <Link href="/blogs">
              <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
                <BiBook className="w-5 h-5 mr-3" />
                Free Blogs by GrowEasy
              </button>
            </Link>
            <Link href="/cost-per-lead-by-industry-and-marketing-channels">
              <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
                <FaChartBar className="w-5 h-5 mr-3" />
                CPL Benchmark Explorer
              </button>
            </Link>
            {/* <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <FaQuestionCircle className="w-5 h-5 mr-3" />
              FAQs
            </button> */}
          </nav>
          <Link href="/marketing-ai-tools">
            <button className="w-full flex items-center justify-center px-4 py-3 mt-auto bg-transparent border-2 border-teal-500 text-white hover:bg-teal-600 rounded-lg transition-colors shadow-lg">
              <BiSearch className="w-6 h-6 mr-2" />
              Explore More Marketing Tools
            </button>
          </Link>
        </div>

        <div className="flex-1 flex flex-col min-h-0">
          <div className="bg-white border-b px-4 sm:px-6 py-4 shadow">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary2 rounded-full flex items-center justify-center">
                <FaCalculator className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <div className="min-w-0">
                <h1 className="text-lg sm:text-xl font-semibold truncate">
                  AI Lead Cost Calculator
                </h1>
                <p className="text-sm sm:text-base text-gray-600 hidden sm:block">
                  This tool helps you calculate and optimize your CPL for any
                  campaign in just a few questions.
                </p>
              </div>
            </div>
          </div>

          {/* Welcome Screen or Messages */}
          <div className="flex-1 overflow-y-auto">
            {!chatStarted ? (
              <div className="flex flex-col items-center justify-center h-full px-4 sm:px-6">
                <div className="text-center max-w-3xl">
                  <h2 className="text-2xl sm:text-4xl md:text-5xl tracking-tight font-semibold mb-4">
                    Welcome! Ready to estimate your lead cost?
                  </h2>
                  <p className="text-lg sm:text-xl text-gray-600 mt-6 sm:mt-10 mb-6">
                    What&apos;s your business industry/product?
                  </p>

                  <div className="flex gap-3 max-w-2xl mx-auto relative">
                    <input
                      type="text"
                      value={currentInput}
                      onChange={(e) => setCurrentInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSendMessage(currentInput);
                        }
                      }}
                      placeholder="Enter your industry..."
                      className="flex-1 px-4 sm:px-8 py-3 sm:py-5 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent placeholder-gray-500 w-full text-sm sm:text-base"
                    />
                    <button
                      onClick={() => handleSendMessage(currentInput)}
                      disabled={sendMessageMutation.isLoading}
                      className="px-4 sm:px-6 py-3 bg-primary2 hover:bg-teal-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-full transition-colors flex items-center justify-center absolute h-full right-0"
                    >
                      {sendMessageMutation.isLoading ? (
                        <div className="animate-spin w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full"></div>
                      ) : (
                        <FaPaperPlane className="w-4 h-4 sm:w-5 sm:h-5" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              /* Chat Messages */
              <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex animate-fade-in gap-2 ${
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.role === 'assistant' && (
                      <Image
                        src={'/images/groweasy-logo-square.png'}
                        alt="GE"
                        width={20}
                        height={20}
                        className="w-8 h-8 sm:w-10 sm:h-10 shrink-0 rounded-full border border-gray-200 overflow-hidden"
                      />
                    )}

                    <div
                      className={`max-w-2xl ${
                        message.role === 'user' ? 'order-1' : ''
                      }`}
                    >
                      <div
                        className={`rounded-2xl text-sm md:text-base px-4 sm:px-6 py-3 shadow-md ${
                          message.role === 'user'
                            ? 'bg-primary2 text-white ml-auto rounded-tr-none'
                            : 'bg-white border border-gray-200 rounded-tl-none'
                        }`}
                      >
                        {message.loading ? (
                          <div className="flex items-center gap-3">
                            <div className="animate-spin w-4 h-4 border-2 border-teal-500 border-t-transparent rounded-full"></div>
                            <span className="text-gray-700 italic">
                              {message.content}
                            </span>
                          </div>
                        ) : message.result ? (
                          <div>
                            <h3 className="font-semibold mb-4 ">
                              {message.content}
                            </h3>
                            <div className="bg-gray-50 rounded-xl p-4 sm:p-6 border border-gray-100">
                              <div className="text-center mb-4 sm:mb-6">
                                <div className="text-2xl sm:text-3xl font-bold text-primary2 mb-1">
                                  {message.result.costRange}
                                </div>
                                <div className="text-xs sm:text-sm text-gray-600">
                                  Industry Average
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 text-xs sm:text-sm mb-4 sm:mb-6">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">City:</span>
                                  <span className="font-medium  ml-1">
                                    {message.result.city}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">
                                    Channel:
                                  </span>
                                  <span className="font-medium ml-1">
                                    {message.result.channel}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">
                                    Product Price:
                                  </span>
                                  <span className="font-medium ml-1">
                                    {message.result.productPrice}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">
                                    Industry:
                                  </span>
                                  <span className="font-medium ml-1">
                                    {message.result.industry}
                                  </span>
                                </div>
                              </div>
                              {/* <div className="flex gap-3">
                              <button className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                                <FaSyncAlt className="w-4 h-4 mr-2" />
                                Recalculate
                              </button>
                              <button className="flex-1 flex items-center justify-center px-4 py-2 bg-primary2 hover:bg-teal-800 text-white rounded-lg transition-colors">
                                <FaDownload className="w-4 h-4 mr-2" />
                                Download
                              </button>
                            </div> */}
                            </div>
                          </div>
                        ) : (
                          <p className="whitespace-pre-line ">
                            {message.content}
                          </p>
                        )}
                      </div>
                    </div>

                    {message.role === 'user' && (
                      <Image
                        src={`https://xvatar.vercel.app/api/avatar/${session?.email}.svg?rounded=25&size=50&userLogo=true`}
                        alt="u"
                        width={20}
                        height={20}
                        className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border border-gray-200 overflow-hidden order-2"
                      />
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Input Area for Chat */}
          {chatStarted && (
            <div className="border-t bg-white p-4 sm:p-6">
              <div className="flex gap-3 max-w-5xl mx-auto relative">
                <input
                  type="text"
                  value={currentInput}
                  onChange={(e) => setCurrentInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage(currentInput);
                    }
                  }}
                  placeholder={
                    sendMessageMutation.isLoading
                      ? 'Sending...'
                      : 'Type your message...'
                  }
                  disabled={sendMessageMutation.isLoading}
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent placeholder-gray-500 disabled:bg-gray-100 text-sm sm:text-base"
                />
                <button
                  onClick={() => handleSendMessage(currentInput)}
                  disabled={
                    !currentInput.trim() || sendMessageMutation.isLoading
                  }
                  className="px-4 sm:px-6 py-3 bg-primary2 hover:bg-teal-800 disabled:bg-primary2/70 disabled:cursor-not-allowed text-white rounded-full transition-colors flex items-center justify-center absolute h-full right-0"
                >
                  {sendMessageMutation.isLoading ? (
                    <div className="animate-spin w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full"></div>
                  ) : (
                    <FaPaperPlane className="w-4 h-4 sm:w-5 sm:h-5" />
                  )}
                </button>
              </div>
            </div>
          )}

          <EmailModal
            isOpen={showEmailModal}
            onClose={() => setShowEmailModal(false)}
            onSubmit={handleEmailSubmit}
            loading={sendMessageMutation.isLoading}
          />
        </div>
      </div>
    </>
  );
}
