import { useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import {
  formatCurrencyAmount,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { QueryParams } from 'src/constants';
import { getCampaignDetails } from 'src/actions/onboarding';
import { getOrderDetails } from 'src/actions/dashboard';
import Button from '@/components/lib/Button';
import CompletePaymentCta from '@/components/lib/PayNowCta';
import Image from 'next/image';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import Link from 'next/link';
import { Currency } from 'src/types/campaigns';

interface IPaymentProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const Payment = (props: IPaymentProps) => {
  const { user, partnerConfig } = props;

  const router = useRouter();

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const campaignId = router.query[QueryParams.CAMPAIGN_ID];
  const orderId = router.query[QueryParams.ORDER_ID] as string;

  const pollingEnabledRef = useRef(true);

  const campaignDetailsResponse = useQuery(
    ['getCampaignDetails', campaignId],
    () => {
      return getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'Payment.getCampaignDetails');
      },
    },
  );

  const orderDetailsResponse = useQuery(
    ['getOrderDetails', orderId],
    () =>
      getOrderDetails({
        orderId,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      refetchInterval: pollingEnabledRef?.current ? 3000 : 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'Payment.getOrderDetails');
      },
      onSuccess: (response) => {
        if (response.data?.status === 'paid') {
          pollingEnabledRef.current = false;
        }
      },
    },
  );

  const skipAndGoToDashboardClick = () => {
    logEvent(EVENT_NAMES.payment_skip_and_go_to_dashboard_clicked);
    void router.push('/dashboard');
  };

  const onPaymentSuccess = () => {
    void router.push('/dashboard');
  };

  const campaignDetails = campaignDetailsResponse?.data?.data ?? null;
  const orderDetails = orderDetailsResponse?.data?.data;

  const campaignName =
    campaignDetails?.details?.business_details?.business_category;
  const paymentAmount =
    orderDetails?.amount /
    ([Currency.IDR, Currency.VND].includes(orderDetails?.currency) ? 1 : 100);

  const renderUi = () => {
    if (orderDetails?.status === 'paid') {
      return (
        <>
          <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
            <div className="mt-12 flex justify-center">
              <Image
                src="/images/common/tick-icon-large.png"
                width="112"
                height="112"
                alt=""
              />
            </div>
            <p className="text-center text-xl font-medium mt-8 text-primary">
              Payment Success
            </p>
            <p className="text-xs mt-8 text-center">
              Thank you for completing the payment of &nbsp;
              {formatCurrencyAmount(paymentAmount, orderDetails.currency)} for
              your campaign {`"${campaignName}"`}.
            </p>
            <p className="text-sm mt-8 text-center">
              Your campaign is now live. You will receive leads via email and in
              the GrowEasy app.
            </p>
          </div>
          <div className="mt-6 flex flex-col">
            <div className="w-full flex justify-center">
              <Link href="/billing-info" className="text-sm text-hyperlink">
                Update Tax/GST Details
              </Link>
            </div>
            <Button
              className="flex items-center justify-center mt-3"
              onClick={() => {
                void router.push('/dashboard');
              }}
            >
              <p>Go to Dashboard</p>
            </Button>
          </div>
        </>
      );
    }
    return (
      <>
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
          <div className="mt-12 flex justify-center">
            <Image
              src="/images/common/pending-payment.svg"
              width="112"
              height="112"
              alt=""
            />
          </div>
          <p className="text-center text-xl font-medium mt-8 text-primary">
            Payment Pending
          </p>
          <p className="text-xs mt-8 text-center">
            Your {`"${campaignName}"`} campaign is ready.
          </p>
          <p className="text-sm mt-8 text-center">
            To start generating leads, please launch the campaign by completing
            your payment of &nbsp;
            {formatCurrencyAmount(paymentAmount, orderDetails.currency)}
          </p>
        </div>
        <div className="mt-6 flex flex-col">
          {partnerConfig?.disablePayment ? (
            <Button
              className="flex items-center justify-center"
              onClick={skipAndGoToDashboardClick}
            >
              <p>Go to Dashboard</p>
            </Button>
          ) : (
            <div className="flex flex-col">
              <CompletePaymentCta
                user={user}
                onSuccess={onPaymentSuccess}
                orderId={orderId}
                automaticallyInitiatePayment
                campaignDetails={campaignDetails}
                partnerConfig={partnerConfig}
              />
              <p
                className="text-hyperlink text-sm text-center mt-3 cursor-pointer font-medium"
                onClick={skipAndGoToDashboardClick}
              >
                Skip and Go to Dashboard
              </p>
            </div>
          )}
        </div>
      </>
    );
  };

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full bg-white h-full">
        {campaignDetails && orderDetails ? (
          <div className="mt-3 mb-3 flex flex-col px-5 flex-1 overflow-hidden">
            {renderUi()}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <SpinnerLoader />
          </div>
        )}
      </div>
    </MobileContainer>
  ) : null;
};

export default Payment;
