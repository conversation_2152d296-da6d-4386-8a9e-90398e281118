import { GetServerSidePropsContext } from 'next';
import {
  LEAD_GEN_SEO_CITIES_LIST,
  LEAD_GEN_SEO_INDUSTRIES_LIST,
} from 'src/constants/seo';
import { IBlogCategory, IPost } from 'src/types/blogs';
import { getPartnerFromHost } from 'src/utils';

const STATIC_URLS = [
  '/',
  '/about-us',
  '/faqs',
  '/privacy-policy',
  '/terms-and-conditions',
  '/blogs',
  '/login',
  '/order-social-media-videos-ads',
  '/order-social-media-videos-ads/order',
  '/digital-marketing-master-class',
  '/tools/facebook-ads-audience-builder',
  '/tools/google-keyword-ideas-generator',
  '/marketing-glossary-essential-terms',
  '/cost-per-lead-by-industry-and-marketing-channels',
  '/marketing-ai-tools',
  '/marketing-ai-tools/lead-cost-calculator',
];

const LEAD_GEN_CITIES_SEO_URLS = LEAD_GEN_SEO_CITIES_LIST.map(
  (item) => `/lead-generation/cities/${item.city.toLowerCase()}`,
);

const LEAD_GEN_INDUSTRIES_SEO_URLS = LEAD_GEN_SEO_INDUSTRIES_LIST.map(
  (item) => `/lead-generation/industry/${item.slug}`,
);

const LEAD_GEN_AD_PLATFORMS_URLS = [
  'google',
  'facebook',
  'instagram',
  'whatsapp',
].map((item) => `/how-to-run-ads/${item}`);

const Sitemap = () => {};

export const getServerSideProps = async (
  context: GetServerSidePropsContext,
) => {
  const { req, res } = context;

  const host =
    (req?.headers['x-forwarded-host'] as string) ?? req?.headers.host;
  const partner = getPartnerFromHost(host);

  // return empty sitemap for white-label solutions
  if (partner) {
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      </urlset>`;
    res.setHeader('Content-Type', 'text/xml');
    res.write(sitemap);
    res.end();

    return {
      props: {},
    };
  }

  const dynamicUrls: string[] = [];
  try {
    const categoriesUrl = `https://api.blogstatic.io/${process.env.BSTATIC_API_KEY}/categories.json`;
    const postsUrl = `https://api.blogstatic.io/${process.env.BSTATIC_API_KEY}/posts.json`;
    const [categoriesRes, postsRes] = await Promise.all([
      fetch(categoriesUrl),
      fetch(postsUrl),
    ]);
    const categories: IBlogCategory[] =
      (await categoriesRes.json()) as IBlogCategory[];
    const categoriesUrls = categories.map(
      (item) => `/blogs/${item.category_url}`,
    );
    dynamicUrls.push(...categoriesUrls);

    const posts: IPost[] = (await postsRes.json()) as IPost[];
    const postsUrls = posts.map((item) => `/blogs/${item.post_url}`);
    dynamicUrls.push(...postsUrls);
  } catch (error) {
    console.error(error);
  }
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${[
        ...STATIC_URLS,
        ...LEAD_GEN_CITIES_SEO_URLS,
        ...LEAD_GEN_INDUSTRIES_SEO_URLS,
        ...LEAD_GEN_AD_PLATFORMS_URLS,
        ...dynamicUrls,
      ]
        .map((url) => {
          return `
            <url>
              <loc>https://groweasy.ai${url}</loc>
              <lastmod>${new Date().toISOString()}</lastmod>
              <changefreq>weekly</changefreq>
              <priority>1.0</priority>
            </url>
          `;
        })
        .join('')}
    </urlset>
  `;

  res.setHeader('Content-Type', 'text/xml');
  res.write(sitemap);
  res.end();

  return {
    props: {},
  };
};

export default Sitemap;
