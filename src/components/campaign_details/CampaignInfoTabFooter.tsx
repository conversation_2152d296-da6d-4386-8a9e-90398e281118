import {
  Currency,
  GrowEasyCampaignStatus,
  ICampaign,
} from 'src/types/campaigns';
import Button from '../lib/Button';
import CompletePaymentCta from '../lib/PayNowCta';
import { IOrderDetails } from 'src/types/payments_invoices';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import { useQuery } from 'react-query';
import { getAdCreditBalance } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  getUsersAdCreditBalanceForACurrency,
  logApiErrorAndShowToastMessage,
} from 'src/utils';

interface ICampaignInfoTabFooterProps {
  orderDetails: IOrderDetails;
  campaignDetails: ICampaign;
  onExtendCampaignClick: () => void;
  onPaymentSuccess: () => void;
  user: IGroweasyUser;
  partnerConfig: IPartnerConfig;
  onDuplicateCampaignClick: () => void;
  onStopCampaignClick: () => void;
}

const CampaignInfoTabFooter = (props: ICampaignInfoTabFooterProps) => {
  const {
    orderDetails,
    campaignDetails,
    onExtendCampaignClick,
    onPaymentSuccess,
    user,
    partnerConfig,
    onDuplicateCampaignClick,
    onStopCampaignClick,
  } = props;

  const adCreditsBalanceResponse = useQuery(
    'getAdCreditBalance',
    () => {
      return getAdCreditBalance({
        headers: getCommonHeaders(user),
        queryParams: {},
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignInfoTabFooter.getAdCreditBalance',
        );
      },
    },
  );
  const adCreditsbalance = adCreditsBalanceResponse?.data?.data ?? null;

  const renderFooter = () => {
    if (!orderDetails) {
      return (
        <div>
          <p className="text-xs text-center text-gray-dark">
            Please reach out to support to complete payment.
          </p>
        </div>
      );
    }
    if (campaignDetails?.status === GrowEasyCampaignStatus.ARCHIVED) {
      return null;
    }
    if (orderDetails?.status === 'paid') {
      //return renderPauseOrResumeCta();
      // for USD payment, show extend but pick old exchange rate from campaign for insights, not live one
      // Insights data might vary a little, todo: monitor
      return (
        <Button onClick={onExtendCampaignClick} className="mt-2">
          <p>Extend Campaign</p>
        </Button>
      );
    } else {
      // if Ad credit has been used while creating order, make sure that sufficient balance exists
      const adCreditAmountToBeConsumed =
        (orderDetails?.ad_credit?.amount_to_be_consumed ?? 0) /
        ([Currency.IDR, Currency.VND].includes(orderDetails?.currency)
          ? 1
          : 100);
      const creditBalance = getUsersAdCreditBalanceForACurrency(
        adCreditsbalance,
        orderDetails.currency,
      );
      if (
        adCreditAmountToBeConsumed &&
        creditBalance < adCreditAmountToBeConsumed
      ) {
        return (
          <div>
            <p className="text-xs text-center text-gray-dark">
              Insufficient ad credit. Please duplicate the campaign and launch
              again.
            </p>
          </div>
        );
      }
      return (
        <CompletePaymentCta
          orderDetails={orderDetails}
          user={user}
          onSuccess={onPaymentSuccess}
          campaignDetails={campaignDetails}
          partnerConfig={partnerConfig}
        />
      );
    }
  };

  const campaignEndDate = new Date(
    campaignDetails.details?.budget_and_scheduling?.end_time,
  );
  const currentDate = new Date();
  const showStopCampaignCta =
    campaignDetails?.status === GrowEasyCampaignStatus.ACTIVE &&
    campaignEndDate > currentDate;

  return (
    <div className="flex flex-col mt-3">
      {renderFooter()}
      <div className="flex items-center mt-2">
        <div className="border border-gray-dark cursor-pointer flex-1 py-3 rounded-lg">
          <p
            className="text-xs text-center text-primary font-medium"
            onClick={onDuplicateCampaignClick}
          >
            Duplicate Campaign
          </p>
        </div>
        {showStopCampaignCta ? <div className="w-3" /> : null}
        {showStopCampaignCta ? (
          <div className="border border-gray-dark cursor-pointer flex-1 py-3 rounded-lg">
            <p
              className="text-xs text-center text-red font-medium"
              onClick={onStopCampaignClick}
            >
              Stop Campaign
            </p>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default CampaignInfoTabFooter;
