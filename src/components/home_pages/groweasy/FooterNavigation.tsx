import React from 'react';
import Link from 'next/link';
import {
  LEAD_GEN_SEO_CITIES_LIST,
  LEAD_GEN_SEO_INDUSTRIES_LIST,
} from 'src/constants/seo';
import BodyV2 from '@/components/lib/typography/BodyV2';

const FooterNavigation = () => {
  const FOOTER_COLUMNS: Array<{
    title: string;
    links: Array<{
      label: string;
      href?: string;
      type?: string;
    }>;
  }> = [
    {
      title: 'Company',
      links: [
        { label: 'About GrowEasy', href: '/about-us' },
        { label: 'FAQs', href: '/faqs' },
        { label: 'Pricing', href: '/pricing' },
        {
          label: 'Contact Us',
          href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/contact_us',
        },
        { label: 'Terms & Conditions', href: '/terms-and-conditions' },
        { label: 'Privacy Policy', href: '/privacy-policy' },
        {
          label: 'Payment Privacy Policy',
          href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/privacy',
        },
        {
          label: 'Cancellation & Refund Policy',
          href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/refund',
        },
        {
          label: 'Shipping & Delivery Policy',
          href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/shipping',
        },
        {
          label: 'Payment Terms & Conditions',
          href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/terms',
        },
      ],
    },
    {
      title: 'GrowEasy Apps & Socials',
      links: [
        {
          label: 'Download GrowEasy Android app on PlayStore',
          href: 'https://play.google.com/store/apps/details?id=me.varunon9.groweasy',
          type: 'link',
        },
        {
          label: 'Download GrowEasy iOS app on AppStore',
          href: 'https://apps.apple.com/us/app/groweasy-ai-lead-generation/id6701542430',
          type: 'link',
        },
        {
          label: 'GrowEasy LinkedIn',
          href: 'https://www.linkedin.com/company/groweasy-ai',
          type: 'link',
        },
        {
          label: 'GrowEasy Instagram',
          href: 'https://www.instagram.com/groweasy_ai/',
          type: 'link',
        },
        {
          label: 'GrowEasy YouTube',
          href: 'https://www.youtube.com/@groweasy-ai',
          type: 'link',
        },
      ],
    },
    {
      title: 'Free AI Digital Marketing Tools',
      links: [
        { label: 'AI Ad Creative Generator', href: 'https://designeasy.ai/' },
        {
          label: 'AI Keyword Planner',
          href: '/tools/google-keyword-ideas-generator',
        },
        {
          label: 'AI Video Ad Script Generator',
          href: '/order-social-media-videos-ads',
        },
        {
          label: 'AI Facebook Audience Generator',
          href: '/tools/facebook-ads-audience-builder',
        },
        {
          label: 'AI Tools for Marketing',
          href: '/marketing-ai-tools',
        },
        {
          label: 'AI Lead Cost Calculator',
          href: '/marketing-ai-tools/lead-cost-calculator',
        },
      ],
    },
    {
      title: 'Digital Marketing and AI',
      links: [
        {
          label: 'AI Lead Generation',
          href: '/blogs/ai-lead-generation',
          type: 'link',
        },
        {
          label: 'AI powered Click to WhatsApp Ads (CTWA)',
          href: '/blogs/click-to-whatsapp-ctwa-ads',
          type: 'link',
        },
        { label: 'DesignEasy (BannerBot)', href: 'https://designeasy.ai' },
        {
          label: 'CPL by industry and marketing channels',
          href: '/cost-per-lead-by-industry-and-marketing-channels',
        },
      ],
    },
    {
      title: 'Free Digital Marketing Courses',
      links: [
        {
          label: 'Digital Marketing Masterclass',
          href: '/digital-marketing-master-class',
          type: 'course',
        },
        {
          label: 'Digital Marketing Keywords',
          href: '/marketing-glossary-essential-terms',
        },
        { label: 'Free Blogs by GrowEasy', href: '/blogs' },
        { label: 'Digital Marketing Blogs', href: '/blogs/digital-marketing' },
        { label: 'Lead Generation Blogs', href: '/blogs/lead-generation' },
        {
          label: 'Best Real Estate Lead Generation Blog',
          href: '/blogs/real-estate-lead-generation',
        },
        { label: 'Industry Wise Lead Generation', type: 'heading' },
        ...LEAD_GEN_SEO_INDUSTRIES_LIST.map((industry) => ({
          label: industry.industry + ' Lead Generation',
          href: `/lead-generation/industry/${industry.slug}`,
          type: 'industry',
        })),
      ],
    },
  ];

  const linkHoverClass =
    'transition-colors duration-200 font-light hover:text-orange-400 hover:underline';

  return (
    <div className="w-full text-white py-8 px-4 sm:px-6 lg:px-8">
      {/* Columns Section */}
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
          {FOOTER_COLUMNS.map((column, columnIndex) => (
            <div key={columnIndex} className="space-y-4">
              <h3 className="text-xs font-semibold text-orange-500 underline underline-offset-4 decoration-gray-200/50">
                {column.title}
              </h3>
              <ul className="space-y-2">
                {column.links.map((link, linkIndex) => {
                  if (link.type === 'heading' || link.type === 'subheading') {
                    return (
                      <li key={linkIndex} className="pt-4 pb-1 block">
                        <span className="text-xs font-semibold text-orange-500 underline underline-offset-4 decoration-gray-200/50">
                          {link.label}
                        </span>
                      </li>
                    );
                  }
                  return (
                    <li key={linkIndex} className="group">
                      <Link href={link.href} target="_blank" className="block">
                        <BodyV2 variant="xxs" className={linkHoverClass}>
                          {link.label}
                        </BodyV2>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Additional Sections */}
      <div className="max-w-7xl mx-auto mt-12">
        <div className="h-0.5 w-full my-8 bg-gray-dark bg-opacity-20" />

        {/* Lead Generation Cities */}
        <div className="text-left space-y-3 mb-6">
          <BodyV2
            variant="xs"
            className="text-orange-500 font-semibold underline underline-offset-4 decoration-gray-200/50"
          >
            Lead generation in cities
          </BodyV2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-2">
            {LEAD_GEN_SEO_CITIES_LIST.map((item, index) => (
              <Link
                href={`/lead-generation/cities/${item.city.toLowerCase()}`}
                key={index}
                target="_blank"
              >
                <BodyV2 variant="xxs" className={linkHoverClass}>
                  Lead Generation in {item.city}
                </BodyV2>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterNavigation;
