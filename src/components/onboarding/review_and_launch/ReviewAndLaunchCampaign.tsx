import { useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import { useMutation, useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import {
  getAdImages,
  getAdVideosDetails,
  updateCampaignStatus,
} from 'src/actions/onboarding';
import {
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  ICampaign,
  ICampaignConfig,
  ILeadgenFormQuestion,
} from 'src/types/campaigns';
import CampaignDetailsComp from '@/components/campaign_details/CampaignDetailsComp';
import Button from '@/components/lib/Button';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import { showToastMessage } from 'src/modules/toast';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { OnboardingStepIds, QueryParams } from 'src/constants';
import DetailedTargetingAccordion from '@/components/campaign_details/DetailedTargetingAccordion';
import EditLeadsQualifyingQuestionsBs from './EditLeadsQualifyingQuestionsBs';

interface IReviewAndLaunchCampaignProps {
  user?: IGroweasyUser;
  campaignDetails?: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  campaignUpdateInProgress: boolean;
  partnerConfig?: IPartnerConfig;
}

const ReviewAndLaunchCampaign = (props: IReviewAndLaunchCampaignProps) => {
  const {
    user,
    campaignDetails,
    saveCampaignDetails,
    campaignUpdateInProgress,
    partnerConfig,
  } = props;

  const [showEditLeadgenFormBs, setShowEditLeadgenFormBs] = useState(false);

  const router = useRouter();

  const campaignId = campaignDetails?.id;

  const adImagesResponse = useQuery(
    ['getAdImages', campaignId],
    () =>
      getAdImages({
        queryParams: {
          ...router.query,
          hashes: JSON.stringify(
            campaignDetails?.details?.ad_banners?.map(
              (item) => item.image?.hash,
            ),
          ),
          [QueryParams.AD_ACCOUNT_ID]:
            campaignDetails?.details?.config?.ad_account_id ?? '',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails &&
        ![
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
        ].includes(campaignDetails?.type),
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'ReviewAndLaunchCampaign.getAdImages',
        );
      },
    },
  );

  const adVideosResponse = useQuery(
    ['getAdVideosDetails', campaignDetails.id],
    () =>
      getAdVideosDetails({
        queryParams: {
          ...router.query,
          ids: campaignDetails?.details?.ad_videos
            ?.map((item) => item.id)
            ?.join(','),
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      enabled: !!campaignDetails?.details?.ad_videos?.filter(
        (item) => !!item.id,
      )?.length,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'ReviewAndLaunchCampaign.getAdVideosDetails',
        );
      },
    },
  );

  const updateCampaignStatusMutation = useMutation(updateCampaignStatus);

  const onLaunchCampaignClick = () => {
    // in case of duplicate campaign or when campaign was created long back and directly being launched
    // check for start date, it should not be in past
    const campaignStartTime = new Date(
      campaignDetails?.details?.budget_and_scheduling?.start_time,
    );
    // till 1 day ago
    if (Date.now() > campaignStartTime.getTime() + 24 * 3600 * 1000) {
      showToastMessage(
        'Campaign start date cannot be in past. Please revisit Ad Budget section',
        'error',
      );
      return;
    }
    logEvent(EVENT_NAMES.launch_campaign_clicked);
    void updateCampaignStatusMutation.mutateAsync(
      {
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
        data: {
          id: campaignDetails.id,
          status: GrowEasyCampaignStatus.ACTIVE,
        },
      },
      {
        onError: (error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'ReviewAndLaunchCampaign.onLaunchCampaignClick',
          );
          logEvent(EVENT_NAMES.campaign_launch_failed);
        },
        onSuccess: (response) => {
          logEvent(EVENT_NAMES.campaign_launched);

          /* event for google search conversion */
          /*const eventLabel = 'AW-11545814763/ik8TCJCCy5YaEOvNvIEr';
          if (window?.gtag) {
            window.gtag('event', 'conversion', {
              send_to: eventLabel,
            });
          }*/
          /* Conversion event snippet ends here */

          void router.push({
            pathname: '/payment',
            query: {
              [QueryParams.CAMPAIGN_ID]: response.data?.id,
              [QueryParams.ORDER_ID]: response.data?.order_id,
            },
          });
        },
      },
    );
  };

  const onSaveLeadgenFormClick = (
    leadgenFormQuestions: ILeadgenFormQuestion[],
  ) => {
    logEvent(EVENT_NAMES.save_leads_qualifying_questions_clicked);
    saveCampaignDetails(
      {
        ...campaignDetails,
        details: {
          ...campaignDetails?.details,
          leadgen_form: {
            ...campaignDetails?.details?.leadgen_form,
            questions: leadgenFormQuestions,
          },
        },
      },
      OnboardingStepIds.REVIEW_AND_LAUNCH,
    );
  };

  const onPagePostCreationSuccess = (
    campaignConfig: Partial<ICampaignConfig>,
  ) => {
    saveCampaignDetails(
      {
        ...campaignDetails,
        details: {
          ...campaignDetails?.details,
          config: {
            ...campaignDetails?.details?.config,
            ...campaignConfig,
          },
        },
      },
      OnboardingStepIds.REVIEW_AND_LAUNCH,
    );
  };

  const adImages = adImagesResponse?.data?.data;

  return (
    <div className="px-4 pt-4 pb-8 overflow-y-scroll flex flex-col flex-1 bg-off-white">
      <div className="flex-1 overflow-y-scroll no-scrollbar pb-1">
        <CampaignDetailsComp
          campaignDetails={campaignDetails}
          adImages={adImages}
          // onEditLeadgenFormClick={() => setShowEditLeadgenFormBs(true)}
          user={user}
          onPagePostCreationSuccess={onPagePostCreationSuccess}
          adVideos={Object.values(adVideosResponse.data?.data ?? {})}
        />
        <DetailedTargetingAccordion
          campaignDetails={campaignDetails}
          className="mt-4"
          user={user}
        />
      </div>
      <p className="text-xs text-center text-gray-dark mt-5">
        You will not be able to edit this campaign after launch
      </p>
      <Button onClick={onLaunchCampaignClick} className="mt-2">
        <p>{partnerConfig ? 'Launch Campaign' : 'Review & Pay'}</p>
      </Button>
      {showEditLeadgenFormBs ? (
        <EditLeadsQualifyingQuestionsBs
          leadgenFormQuestions={
            campaignDetails?.details?.leadgen_form?.questions ?? []
          }
          onSaveClick={onSaveLeadgenFormClick}
          onClose={() => setShowEditLeadgenFormBs(false)}
          campaignUpdateInProgress={campaignUpdateInProgress}
        />
      ) : null}
      {updateCampaignStatusMutation.isLoading ? <FullScreenLoader /> : null}
    </div>
  );
};

export default ReviewAndLaunchCampaign;
